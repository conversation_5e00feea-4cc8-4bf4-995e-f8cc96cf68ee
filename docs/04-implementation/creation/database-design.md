# 创作模块数据库设计

##  概述

本文档详细说明创作模块的数据库设计，包括本地SQLite和云端PostgreSQL的表结构、索引设计和数据同步策略。采用本地优先的设计理念，确保数据的一致性和可靠性。

## 🏗️ 数据库架构

### 双数据库架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Client Side (Flutter)                    │
├─────────────────────────────────────────────────────────────┤
│                    Local SQLite Database                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    book     │  │    card     │  │   card_content      │  │
│  │    user     │  │             │  │   sync_record       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ Sync API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Server Side (FastAPI)                     │
├─────────────────────────────────────────────────────────────┤
│                  Cloud PostgreSQL Database                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    book     │  │    card     │  │   card_content      │  │
│  │    user     │  │             │  │    sync_log         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 📊 本地SQLite数据库设计

### 1. user表 (用户信息)

```sql
CREATE TABLE user (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    avatar_url VARCHAR(512),
    is_vip BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0            -- 是否有未同步的更改
);

-- 索引
CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_is_dirty ON user(is_dirty);
```

### 2. book表 (书籍)

```sql
CREATE TABLE book (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    user_id VARCHAR(22) NOT NULL,         -- 用户ID (引用user.id)
    name VARCHAR(100) NOT NULL,
    brief TEXT,
    cover VARCHAR(512),
    type VARCHAR(20) DEFAULT 'free',      -- free, private, vip
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    is_deleted BOOLEAN DEFAULT 0,         -- 软删除标记

    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_book_user_id ON book(user_id);
CREATE INDEX idx_book_user_created ON book(user_id, created_at DESC);
CREATE INDEX idx_book_user_name ON book(user_id, name);
CREATE INDEX idx_book_type ON book(type);
CREATE INDEX idx_book_is_dirty ON book(is_dirty);
CREATE INDEX idx_book_is_deleted ON book(is_deleted);

-- 全文搜索索引
CREATE VIRTUAL TABLE book_fts USING fts5(
    name, brief, content='book', content_rowid='id'
);

-- 触发器：维护全文搜索索引
CREATE TRIGGER book_fts_insert AFTER INSERT ON book BEGIN
    INSERT INTO book_fts(rowid, name, brief)
    VALUES (new.id, new.name, new.brief);
END;

CREATE TRIGGER book_fts_update AFTER UPDATE ON book BEGIN
    UPDATE book_fts SET name = new.name, brief = new.brief
    WHERE rowid = new.id;
END;

CREATE TRIGGER book_fts_delete AFTER DELETE ON book BEGIN
    DELETE FROM book_fts WHERE rowid = old.id;
END;
```

### 3. card表 (卡片)

```sql
CREATE TABLE card (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    user_id VARCHAR(22) NOT NULL,         -- 用户ID (引用user.id)
    book_id VARCHAR(22),                  -- 所属书籍ID (引用book.id)
    type VARCHAR(50) DEFAULT 'general',   -- 卡片类型
    title VARCHAR(128),                   -- 卡片标题
    question TEXT,                        -- 问题内容（可为NULL）
    answer TEXT,                          -- 答案内容（可为NULL）
    extra TEXT,                           -- JSON格式的扩展数据
    order_index INTEGER DEFAULT 0,        -- 卡片在书籍中的顺序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    is_deleted BOOLEAN DEFAULT 0,         -- 软删除标记
    is_reviewed BOOLEAN DEFAULT 0,        -- 是否已复习

    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES book(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_card_user_id ON card(user_id);
CREATE INDEX idx_card_book_id ON card(book_id);
CREATE INDEX idx_card_user_created ON card(user_id, created_at DESC);
CREATE INDEX idx_card_user_type ON card(user_id, type);
CREATE INDEX idx_card_book_order ON card(book_id, order_index);
CREATE INDEX idx_card_title ON card(title);
CREATE INDEX idx_card_is_dirty ON card(is_dirty);
CREATE INDEX idx_card_is_deleted ON card(is_deleted);

-- 全文搜索索引
CREATE VIRTUAL TABLE card_fts USING fts5(
    title, question, answer, content='card', content_rowid='id'
);

-- 触发器：维护全文搜索索引
CREATE TRIGGER card_fts_insert AFTER INSERT ON card BEGIN
    INSERT INTO card_fts(rowid, title, question, answer)
    VALUES (new.id, new.title, new.question, new.answer);
END;

CREATE TRIGGER card_fts_update AFTER UPDATE ON card BEGIN
    UPDATE card_fts SET title = new.title, question = new.question, answer = new.answer
    WHERE rowid = new.id;
END;

CREATE TRIGGER card_fts_delete AFTER DELETE ON card BEGIN
    DELETE FROM card_fts WHERE rowid = old.id;
END;
```

### 4. card_content表 (卡片内容)

```sql
CREATE TABLE card_content (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    card_id VARCHAR(22) NOT NULL,         -- 所属卡片ID (引用card.id)
    content_type VARCHAR(50) NOT NULL,    -- text, image, audio, video, option
    content_text TEXT,                    -- 文本内容
    content_url VARCHAR(512),             -- 媒体文件URL
    display_order INTEGER DEFAULT 0,      -- 内容显示顺序
    is_correct BOOLEAN DEFAULT 0,         -- 是否为正确答案（用于选择题等）
    metadata TEXT,                        -- JSON格式的元数据（文件大小、MIME类型等）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,                   -- 最后同步时间
    is_dirty BOOLEAN DEFAULT 0,           -- 是否有未同步的更改
    is_deleted BOOLEAN DEFAULT 0,         -- 软删除标记

    FOREIGN KEY (card_id) REFERENCES card(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_card_content_card_id ON card_content(card_id);
CREATE INDEX idx_card_content_card_order ON card_content(card_id, display_order);
CREATE INDEX idx_card_content_type ON card_content(content_type);
CREATE INDEX idx_card_content_is_dirty ON card_content(is_dirty);
CREATE INDEX idx_card_content_is_deleted ON card_content(is_deleted);
```

### 5. sync_record表 (同步记录)

```sql
CREATE TABLE sync_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type VARCHAR(50) NOT NULL,     -- user, book, card, card_content
    entity_id VARCHAR(22) NOT NULL,       -- 实体ID (shortuuid)
    operation VARCHAR(20) NOT NULL,       -- create, update, delete
    status VARCHAR(20) DEFAULT 'pending', -- pending, success, failed
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_sync_record_status ON sync_record(status);
CREATE INDEX idx_sync_record_entity ON sync_record(entity_type, entity_id);
CREATE INDEX idx_sync_record_created ON sync_record(created_at);
```

## 🌐 云端PostgreSQL数据库设计

### 1. user表 (用户信息)

```sql
CREATE TABLE "user" (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    avatar_url VARCHAR(512),
    is_vip BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_user_username ON "user"(username);
CREATE UNIQUE INDEX idx_user_email ON "user"(email) WHERE email IS NOT NULL;
```

### 2. book表 (书籍)

```sql
CREATE TABLE book (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    user_id VARCHAR(22) NOT NULL,         -- 用户ID (引用user.id)
    name VARCHAR(100) NOT NULL,
    brief TEXT,
    cover VARCHAR(512),
    type VARCHAR(20) DEFAULT 'free',      -- free, private, vip
    card_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_book_user_id ON book(user_id);
CREATE INDEX idx_book_user_created ON book(user_id, created_at DESC);
CREATE INDEX idx_book_user_name ON book(user_id, name);
CREATE INDEX idx_book_type ON book(type);

-- 全文搜索索引
CREATE INDEX idx_book_search ON book USING gin(to_tsvector('english', name || ' ' || COALESCE(brief, '')));
```

### 3. card表 (卡片)

```sql
CREATE TABLE card (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    user_id VARCHAR(22) NOT NULL,         -- 用户ID (引用user.id)
    book_id VARCHAR(22),                  -- 所属书籍ID (引用book.id)
    type VARCHAR(50) DEFAULT 'general',   -- 卡片类型
    type_version INTEGER DEFAULT 1,
    title VARCHAR(128),                   -- 卡片标题
    question TEXT,                        -- 问题内容（可为NULL）
    answer TEXT,                          -- 答案内容（可为NULL）
    notes TEXT,                           -- 笔记内容
    extra JSONB DEFAULT '{}',             -- JSON格式的扩展数据
    order_index INTEGER DEFAULT 0,        -- 卡片在书籍中的顺序
    schedule_id INTEGER,                  -- 学习计划ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES book(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_card_user_id ON card(user_id);
CREATE INDEX idx_card_book_id ON card(book_id);
CREATE INDEX idx_card_user_created ON card(user_id, created_at DESC);
CREATE INDEX idx_card_user_type ON card(user_id, type);
CREATE INDEX idx_card_book_order ON card(book_id, order_index);
CREATE INDEX idx_card_title ON card(title);

-- JSONB索引
CREATE INDEX idx_card_extra ON card USING gin(extra);

-- 全文搜索索引
CREATE INDEX idx_card_search ON card USING gin(
    to_tsvector('english',
        COALESCE(title, '') || ' ' ||
        COALESCE(question, '') || ' ' ||
        COALESCE(answer, '')
    )
);
```

### 4. card_content表 (卡片内容)

```sql
CREATE TABLE card_content (
    id VARCHAR(22) PRIMARY KEY,           -- 使用 shortuuid 作为主键
    card_id VARCHAR(22) NOT NULL,         -- 所属卡片ID (引用card.id)
    content_type VARCHAR(50) NOT NULL,    -- text, image, audio, video, option
    content_text TEXT,                    -- 文本内容
    content_url VARCHAR(512),             -- 媒体文件URL
    display_order INTEGER DEFAULT 0,      -- 内容显示顺序
    is_correct BOOLEAN DEFAULT FALSE,     -- 是否为正确答案（用于选择题等）
    metadata JSONB DEFAULT '{}',          -- JSON格式的元数据（文件大小、MIME类型等）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (card_id) REFERENCES card(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_card_content_card_id ON card_content(card_id);
CREATE INDEX idx_card_content_card_order ON card_content(card_id, display_order);
CREATE INDEX idx_card_content_type ON card_content(content_type);

-- JSONB索引
CREATE INDEX idx_card_content_metadata ON card_content USING gin(metadata);
```

### 3. 数据库触发器

```sql
-- 自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_updated_at
    BEFORE UPDATE ON "user"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_updated_at
    BEFORE UPDATE ON book
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_card_updated_at
    BEFORE UPDATE ON card
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_card_content_updated_at
    BEFORE UPDATE ON card_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自动更新书籍卡片数量
CREATE OR REPLACE FUNCTION update_book_card_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE books SET card_count = card_count + 1 WHERE id = NEW.book_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE books SET card_count = card_count - 1 WHERE id = OLD.book_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- 如果卡片从一个书籍移动到另一个书籍
        IF OLD.book_id != NEW.book_id THEN
            UPDATE books SET card_count = card_count - 1 WHERE id = OLD.book_id;
            UPDATE books SET card_count = card_count + 1 WHERE id = NEW.book_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_book_card_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON cards
    FOR EACH ROW EXECUTE FUNCTION update_book_card_count();
```

## 🔄 数据同步策略

### 1. 同步状态管理

```sql
-- 本地同步状态枚举
-- pending: 等待同步
-- syncing: 正在同步
-- success: 同步成功
-- failed: 同步失败
-- conflict: 存在冲突

-- 冲突解决策略
-- local_wins: 本地优先
-- server_wins: 服务器优先
-- manual: 手动解决
```

### 2. 增量同步机制

```sql
-- 获取需要上传的本地数据
SELECT * FROM books
WHERE is_dirty = 1 AND is_deleted = 0
ORDER BY updated_at ASC;

-- 获取需要下载的服务器数据
SELECT * FROM books
WHERE user_id = ? AND updated_at > ?
ORDER BY updated_at ASC;

-- 冲突检测查询（使用shortuuid，不再需要server_id映射）
SELECT
    l.id,
    l.updated_at as local_updated,
    s.updated_at as server_updated
FROM books l
JOIN books s ON l.id = s.id
WHERE l.is_dirty = 1
  AND l.updated_at < s.updated_at;
```

### 3. 数据一致性保证

```sql
-- 事务性同步操作
BEGIN TRANSACTION;

-- 1. 创建同步记录
INSERT INTO sync_records (entity_type, entity_id, operation, status)
VALUES ('book', ?, 'update', 'pending');

-- 2. 更新本地数据
UPDATE books SET
    name = ?,
    brief = ?,
    updated_at = CURRENT_TIMESTAMP,
    is_dirty = 1
WHERE id = ?;

-- 3. 提交事务
COMMIT;
```

## 📈 性能优化

### 1. 查询优化

```sql
-- 使用复合索引优化常用查询
CREATE INDEX idx_books_user_privacy_created 
ON books(user_id, privacy, created_at DESC);

-- 使用部分索引优化特定条件查询
CREATE INDEX idx_books_dirty 
ON books(user_id, updated_at) 
WHERE is_dirty = 1;

-- 使用表达式索引优化搜索
CREATE INDEX idx_books_name_lower 
ON books(lower(name));
```

### 2. 分区策略

```sql
-- 按用户ID分区（适用于大量用户场景）
CREATE TABLE books_partitioned (
    LIKE books INCLUDING ALL
) PARTITION BY HASH (user_id);

-- 创建分区表
CREATE TABLE books_partition_0 PARTITION OF books_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 0);

CREATE TABLE books_partition_1 PARTITION OF books_partitioned
FOR VALUES WITH (MODULUS 4, REMAINDER 1);
```

### 3. 缓存策略

```sql
-- 物化视图：用户统计信息
CREATE MATERIALIZED VIEW user_stats AS
SELECT 
    user_id,
    COUNT(*) as total_books,
    COUNT(CASE WHEN privacy = 'free' THEN 1 END) as public_books,
    SUM(card_count) as total_cards,
    MAX(updated_at) as last_activity
FROM books
WHERE is_deleted = 0
GROUP BY user_id;

-- 定期刷新物化视图
CREATE OR REPLACE FUNCTION refresh_user_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_stats;
END;
$$ LANGUAGE plpgsql;
```

## 🔒 数据安全

### 1. 数据加密

```sql
-- 敏感字段加密存储
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 加密存储用户邮箱
UPDATE users SET email = pgp_sym_encrypt(email, 'encryption_key');

-- 解密查询
SELECT pgp_sym_decrypt(email::bytea, 'encryption_key') as email FROM users;
```

### 2. 访问控制

```sql
-- 行级安全策略
ALTER TABLE books ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的书籍
CREATE POLICY books_user_policy ON books
FOR ALL TO app_user
USING (user_id = current_setting('app.current_user_id')::integer);
```

### 3. 审计日志

```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    user_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 审计触发器
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (table_name, operation, user_id, old_values, new_values)
    VALUES (
        TG_TABLE_NAME,
        TG_OP,
        COALESCE(NEW.user_id, OLD.user_id),
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN row_to_json(NEW) ELSE NULL END
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

## 🔄 设计变更说明

### 主要变更内容

1. **使用 shortuuid 作为主键**
   - `books.id`: `INTEGER` → `VARCHAR(22)`
   - `cards.id`: `INTEGER` → `VARCHAR(22)`
   - `card_contents.id`: `INTEGER` → `VARCHAR(22)`
   - 移除了所有 `server_id` 字段，简化同步逻辑

2. **简化书籍-卡片关系**
   - 删除 `book_card` 中间表
   - 在 `cards` 表中保留 `book_id` 字段实现一对多关系
   - 添加 `order_index` 字段到 `cards` 表管理顺序

3. **重构卡片内容存储**
   - `card_assets` 表重命名为 `card_contents`
   - 更清晰的字段命名和结构
   - 支持多种内容类型的统一存储

### 设计优势

✅ **简化同步机制**: 使用全局唯一的 shortuuid，避免客户端-服务器 ID 映射问题
✅ **减少数据冗余**: 移除不必要的中间表，直接使用外键关系
✅ **提高可维护性**: 更清晰的表结构和命名规范
✅ **增强扩展性**: 统一的内容存储模型，便于支持新的内容类型

### 迁移注意事项

⚠️ **现有数据迁移**: 需要为现有记录生成 shortuuid
⚠️ **应用层适配**: 客户端和服务端代码需要相应调整
⚠️ **索引重建**: 字符串主键的索引性能需要监控

---

**注意**：本文档描述的数据库设计应根据实际业务需求和数据量进行调整。在生产环境中，还需要考虑备份策略、监控告警、容灾恢复等方面的设计。
