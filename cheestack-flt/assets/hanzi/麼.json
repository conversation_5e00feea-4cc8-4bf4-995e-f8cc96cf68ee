{"strokes": ["M 492 815 Q 513 797 535 776 Q 551 763 568 764 Q 580 765 584 780 Q 588 796 577 829 Q 565 856 484 868 Q 466 871 460 867 Q 454 863 457 849 Q 461 836 492 815 Z", "M 306 684 Q 342 674 441 687 Q 744 732 767 745 Q 767 746 769 746 Q 778 753 774 761 Q 768 773 742 780 Q 718 786 620 760 Q 443 720 302 713 Q 301 713 299 712 C 269 709 277 690 306 684 Z", "M 299 712 Q 283 727 254 739 Q 235 749 221 745 Q 208 738 215 723 Q 237 689 234 632 Q 218 437 202 359 Q 190 314 177 274 Q 135 159 51 28 Q 45 21 44 15 Q 41 3 52 6 Q 82 12 161 130 Q 182 164 201 204 Q 271 358 288 565 Q 300 679 306 684 C 311 703 311 703 299 712 Z", "M 446 547 Q 471 554 500 560 Q 524 566 528 569 Q 535 576 531 583 Q 524 592 501 597 Q 462 603 448 589 L 410 577 Q 349 565 326 562 Q 296 556 318 544 Q 345 531 410 540 L 412 540 L 446 547 Z", "M 412 540 Q 412 417 404 388 Q 397 367 413 329 Q 417 319 421 317 Q 425 313 429 318 Q 444 328 445 365 Q 444 384 446 547 L 448 589 Q 449 607 454 620 Q 458 629 457 637 Q 453 643 421 662 Q 408 672 395 663 Q 392 660 396 651 Q 409 633 409 615 Q 409 597 410 577 L 412 540 Z", "M 335 504 Q 348 468 316 392 Q 301 359 279 333 Q 275 329 273 325 Q 270 318 277 319 Q 301 319 334 362 Q 394 461 388 484 Q 379 497 361 510 Q 351 519 341 517 Q 332 514 335 504 Z", "M 477 511 Q 486 490 483 414 Q 480 399 496 379 Q 502 372 508 379 Q 517 397 520 463 Q 520 478 523 492 Q 527 507 514 514 Q 487 529 479 525 Q 475 521 477 511 Z", "M 696 568 Q 735 578 777 586 Q 807 593 812 598 Q 819 605 815 613 Q 808 623 781 630 Q 763 633 697 613 L 655 600 Q 615 591 567 586 Q 534 580 558 565 Q 600 544 650 559 Q 651 559 656 559 L 696 568 Z", "M 656 559 Q 656 451 649 424 Q 642 394 659 358 Q 663 348 668 345 Q 672 341 677 346 Q 681 349 689 362 Q 693 377 696 568 L 697 613 Q 698 638 704 655 Q 710 665 708 674 Q 704 680 667 702 Q 651 712 638 703 Q 635 699 639 690 Q 651 674 655 600 L 656 559 Z", "M 577 520 Q 584 511 582 502 Q 572 454 537 387 Q 533 380 531 376 Q 530 369 538 372 Q 568 379 617 473 Q 624 486 631 494 Q 635 501 632 509 Q 628 516 610 527 Q 594 536 582 534 Q 572 533 577 520 Z", "M 744 485 Q 738 392 759 370 Q 801 334 879 349 Q 880 350 882 350 Q 900 354 918 369 Q 933 382 922 402 Q 912 421 895 477 Q 894 490 889 494 Q 885 495 881 482 Q 862 419 848 400 Q 845 396 834 394 Q 810 388 791 398 Q 775 407 775 442 Q 776 476 785 521 Q 789 534 780 541 Q 770 551 754 557 Q 744 560 738 557 Q 734 554 737 541 Q 747 513 744 485 Z", "M 542 178 Q 481 181 430 182 Q 414 183 423 192 Q 477 237 525 268 Q 541 280 541 287 Q 542 300 516 338 Q 506 354 488 355 Q 478 355 479 340 Q 482 306 405 227 Q 389 211 362 198 Q 343 188 342 176 Q 343 158 353 139 Q 360 127 374 132 Q 408 145 514 153 C 544 155 572 177 542 178 Z", "M 669 74 Q 572 64 459 42 Q 441 38 454 51 Q 616 195 685 230 Q 700 237 696 250 Q 689 272 659 300 Q 644 313 634 312 Q 622 311 621 294 Q 615 255 542 178 L 514 153 Q 391 45 369 39 Q 353 32 353 17 Q 352 2 376 -22 Q 385 -37 402 -26 Q 517 28 681 53 C 711 58 699 77 669 74 Z", "M 681 53 Q 733 -34 743 -39 Q 752 -43 762 -34 Q 778 -24 774 25 Q 774 50 760 71 Q 747 87 712 109 Q 673 134 652 145 Q 643 152 638 140 Q 634 127 643 113 Q 656 95 669 74 L 681 53 Z"], "medians": [[[467, 859], [542, 817], [566, 782]], [[303, 706], [332, 697], [453, 706], [659, 747], [735, 759], [763, 757]], [[227, 732], [268, 686], [253, 509], [228, 355], [198, 259], [165, 182], [103, 76], [52, 14]], [[319, 554], [349, 550], [398, 555], [468, 576], [521, 578]], [[404, 658], [422, 643], [431, 624], [424, 328]], [[345, 507], [363, 472], [347, 419], [318, 362], [280, 326]], [[483, 517], [502, 496], [502, 386]], [[560, 577], [583, 571], [623, 573], [732, 601], [804, 607]], [[647, 697], [677, 663], [671, 356]], [[588, 524], [606, 501], [596, 472], [563, 409], [538, 380]], [[743, 552], [762, 530], [765, 516], [759, 473], [763, 406], [777, 382], [796, 372], [852, 372], [883, 388], [886, 488]], [[489, 344], [502, 316], [502, 295], [403, 199], [392, 174], [441, 163], [511, 166], [534, 177]], [[636, 297], [646, 274], [646, 251], [555, 162], [435, 60], [423, 26], [451, 19], [649, 61], [663, 63], [672, 55]], [[648, 133], [730, 47], [748, -3], [748, -29]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}