---
type: "agent_requested"
description: "Example description"
---
# Flutter 开发规范指南

## 🎯 核心原则

- 始终声明每个变量和函数的类型（参数和返回值）
- 避免使用任何类型（any），创建必要的类型
- 不要在函数内部留空行
- 每个文件只导出一个主要内容
- 使用 GetX 进行状态管理, 使用GetBuilder和update()的方式, 不要使用obs更新状态
- 遵循 MVVM 架构模式
- 保持单一职责原则
- 高内聚低耦合的模块设计

## 📁 项目结构规范

### 标准项目结构
```
flutter_project/
├── android/                 # Android 原生代码
├── ios/                     # iOS 原生代码
├── web/                     # Web 平台代码
├── assets/                  # 资源文件
│   ├── fonts/              # 字体文件
│   ├── images/             # 图片资源
│   └── others/             # 其他资源
├── lib/                     # 主要代码目录
│   ├── core/               # 核心基础设施
│   │   ├── network/        # 网络层
│   │   ├── storage/        # 存储层
│   │   ├── theme/          # 主题配置
│   │   ├── constants/      # 常量定义
│   │   └── utils/          # 工具函数
│   ├── routes/             # 路由配置
│   │   └── app_routes.dart # 整个app的路由配置
│   ├── shared/             # 共享资源
│   │   ├── widgets/        # 通用UI组件
│   │   ├── models/         # 共享数据模型
│   │   ├── services/       # 共享服务
│   │   └── extensions/     # 扩展方法
│   ├── features/           # 功能模块
│   │   ├── auth/           # 认证功能模块
│   │   │   ├── models/     # 数据模型
│   │   │   │   └── auth_model.dart
│   │   │   │   └── xxx_model.dart
│   │   │   ├── apis/   # 网络请求服务
│   │   │   │   └── xxx_api.dart # api
│   │   │   │   └── xxx_api.dart # api
│   │   │   ├── controllers/ # 状态管理控制器
│   │   │   │   └── auth_controller.dart
│   │   │   │   └── xxx_controller.dart
│   │   │   ├── pages/      # 表现层
│   │   │   │   ├── xxx_page.dart # 页面,需配置路由
│   │   │   │   ├── xxx_page.dart # 页面,需配置路由
│   │   │   │   └── widgets/ # 功能特定组件
│   │   │   │       ├── auth_form.dart
│   │   │   │       └── auth_button.dart
│   │   │   ├── index.dart/ # 采取export方式导出模组文件
│   │   └── other_feature/           # 其他功能模块...
│   └── main.dart           # 应用入口
├── test/                    # 测试模块
└── pubspec.yaml            # 依赖配置
```

### 模块化设计原则
- **高内聚低耦合**：模块内部紧密相关，模块间依赖最小化
- **分层架构**：UI层、业务逻辑层、数据层分离

## 📝 命名规范
- **类**: PascalCase
- **变量、函数、方法**: camelCase
- **文件和目录**: underscores_case
- **常量**: UPPERCASE
- **函数**: 以动词开头
- **布尔变量**: 使用 is/has/can/should 等前缀
- **避免缩写**: 使用完整单词（除了标准缩写如 API、URL）

## 🏗️ Flutter 架构规范

### GetX 状态管理
- **状态更新**: 使用 `GetBuilder` 配合 `update()` 更新，禁用 `obs` 更新状态
- **控制器**: 使用控制器模式与 GetX 处理业务逻辑
- **依赖注入**: 使用 GetX 进行依赖管理
- **状态保持**: 需要保持状态时使用 `keepAlive`

### 架构模式
- **MVVM 架构**: Model-View-ViewModel 分离
- **Repository 模式**: 数据持久化使用存储库模式
- **服务层**: 业务服务层处理复杂逻辑
- **扩展管理**: 使用扩展管理可重用代码

### Widget 设计原则
- **避免深度嵌套**: 将复杂的小部件树拆分为更小的可重用组件
- **使用 const 构造函数**: 减少重建次数，提高性能
- **单一职责**: 每个 Widget 只负责一个功能
- **扁平化结构**: 保持 Widget 树扁平化，便于状态管理

### 主题和样式
- **ThemeData 管理主题**: 统一应用主题
- **AppLocalizations 管理翻译**: 国际化支持
- **常量管理**: 使用常量管理常量值

## 🎨 颜色和主题规范

### 🔥 强制颜色使用规则
- **必须使用**: `Theme.of(Get.context!).colorScheme` 获取所有颜色
- **禁止使用**: `AppTheme` 中的颜色属性（如 `AppTheme.onSurface`）
- **禁止硬编码**: 不使用 `Color(0xFFXXXXXX)` 构造器
- **禁用静态颜色**: 不使用 `Colors.xxx`（除了 `Colors.transparent`）

## 📄 页面代码规范

### 🏗️ 标准模块化架构

**项目采用标准的模块化架构，每个文件独立管理自己的导入依赖：**

1. **导入规则**：每个文件独立管理自己需要的导入
2. **引用规则**：可以直接导入需要的具体文件
3. **模块化原则**：清晰的职责分离，便于维护和测试


## 📱 响应式布局和屏幕适配规范

### 🔥 强制使用 flutter_screenutil
**所有UI组件必须使用 `flutter_screenutil` 进行屏幕适配，确保在不同设备上保持一致的用户体验。**

### 📱 响应式布局原则

### 设备适配策略
- **手机优先**: 以手机屏幕为基准设计
- **平板扩展**: 使用响应式布局适配平板
- **断点设计**: 根据屏幕宽度调整布局

### 命名规范
- **控制器**: `[ModuleName]Controller` 格式
- **页面 Page**: `[ModuleName]Page` 格式
- **服务类**: `[ModuleName]Service` 格式
- **模型类**: `[ModuleName]Model` 格式
- **组件**: `[Prefix][WidgetName]` 格式

### 功能规范
#### 控制器职责
- 处理数据获取和业务逻辑
- 提供导航方法
- 管理页面状态
- 与服务层交互

#### 视图原则
- 使用 `GetBuilder` 绑定控制器
- 需要保持状态的页面实现 `AutomaticKeepAliveClientMixin`
- 组件独立导出，便于复用和测试
- 所有页面导航通过控制器方法进行，使用 `Get.toNamed`
- 必须使用 `Theme.of(Get.context!).colorScheme` 获取颜色和文字大小

#### 服务层原则
- 处理网络请求和数据持久化
- 提供清晰的API接口
- 处理异常和错误状态
- 独立于UI层，便于测试

## ⚡ 性能优化规范

### Widget 优化
- **使用 const 构造函数**: 减少重建次数
- **提取子组件**: 避免不必要的重复构建
- **合理使用 Key**: 为列表项使用唯一 Key

### 列表优化
- **使用 ListView.builder**: 而不是 ListView
- **实现懒加载**: 大数据集使用分页加载
- **使用 AutomaticKeepAliveClientMixin**: 保持页面状态

### 图片优化
- **使用 cached_network_image**: 缓存网络图片
- **限制内存缓存尺寸**: 设置 memCacheWidth 和 memCacheHeight
- **合理的图片格式**: 选择适当的图片格式和压缩

## 🛠️ 开发工具配置

### Flutter 环境配置
- **Flutter路径**: `/Users/<USER>/dev/flutter/bin/flutter`

#### 测试文件组织结构
```
test/
├── theme_test.dart              # 主题测试
├── color_constants_test.dart    # 颜色常量测试
├── pages/                       # 页面测试
│   ├── home_page_test.dart
│   ├── auth_page_test.dart
│   └── review_page_test.dart
├── widgets/                     # 组件测试
│   └── widgets_test.dart
├── integration/                 # 集成测试
│   └── color_theme_integration_test.dart
└── test_helpers/               # 测试辅助工具
    └── test_utils.dart
```
### 代码生成
- 使用 `build_runner` 从注解生成代码（Freezed、JSON 序列化）
- 修改注解类后运行: `flutter pub run build_runner build --delete-conflicting-outputs`

### UI 和样式
- 使用 Flutter 内置组件并创建自定义组件
- 使用 `LayoutBuilder` 或 `MediaQuery` 实现响应式设计
- 使用主题保持应用一致的样式

### 参考资源
- 界面视图库: [ducafe_ui_core packages](https://pub.dev/packages/ducafe_ui_core)
- 遵循官方 Flutter 文档获取最佳实践

### 文档规范
- 为复杂逻辑和非显而易见的代码决策编写文档
- 使用中文注释说明关键业务逻辑
- 保持注释与代码同步更新

## 🧪 测试规范

### 测试执行命令
使用`flutter test xxx` 的方法
***禁止使用flutter run 进行测试***

### 测试最佳实践
- **AAA模式**: Arrange（准备）、Act（执行）、Assert（断言）
- **测试隔离**: 每个测试独立，不依赖其他测试
- **清理资源**: 在tearDown中清理GetX状态和其他资源
- **有意义的测试名**: 使用中文描述测试目的
- **边界测试**: 测试正常情况和边界情况


